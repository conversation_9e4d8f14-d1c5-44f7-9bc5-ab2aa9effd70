!pip install -q langchain-google-genai langchain-core langchain pinecone python-dotenv langchain-community


from dotenv import load_dotenv
import os

load_dotenv()

os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_API_KEY")
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")


import pinecone
from langchain.document_loaders import DirectoryLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain.vectorstores import Pinecone

# Init Pinecone
pinecone.init(api_key=PINECONE_API_KEY)

# Load and split documents
loader = DirectoryLoader('./data', loader_cls=TextLoader)
docs = loader.load()

splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)
splits = splitter.split_documents(docs)

# Create embeddings
embedding = GoogleGenerativeAIEmbeddings(model="models/embedding-001")

# Upload vectors to Pinecone
vectorstore = Pinecone.from_documents(
    documents=splits,
    embedding=embedding,
    index_name=PINECONE_INDEX_NAME
)


from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.chains import RetrievalQA

llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0.2)

retriever = Pinecone.from_existing_index(
    index_name=PINECONE_INDEX_NAME,
    embedding=embedding
).as_retriever()

qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    retriever=retriever,
    return_source_documents=True
)


query = "What are the symptoms of atrial fibrillation?"
result = qa_chain.invoke(query)
print("Answer:", result["result"])
